﻿using Azure.Core;
using FluentBlue.Shared;
using FluentBlue.UI.Main.Pages.Resources;
using FluentBlue.UI.Main.Shared;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Components.Web;

namespace FluentBlue.UI.Main.Pages
{
    public partial class Login
    {
        public class LoginData
        {
            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            public string Username { get; set; } = string.Empty;

            [Required(ErrorMessageResourceName = "FieldRequired", ErrorMessageResourceType = typeof(Main.GlobalResource), AllowEmptyStrings = false)]
            public string Password { get; set; } = string.Empty;
        }

        private LoginData loginData = new LoginData();
        private bool loginInProgress = false;
        private string loginResultMessage = " ";

        protected override void OnInitialized()
        {

        }

        private async Task OnPasswordKeyDown(KeyboardEventArgs e)
        {
            if (e.Key == "Enter")
            {
                // Small delay to ensure the binding has updated
                await Task.Delay(10);
                await SignInAsync();
            }
        }

        private async void OnSignIn()
        {
            await SignInAsync();
        }

        private async Task SignInAsync()
        {
            try
            {
                this.loginInProgress = true;

                WebApi.Shared.Response.LoginResponse loginResponse = await authenticationWebApiClient.Login(loginData.Username, loginData.Password);
                if (loginResponse.LoginResult == WebApi.Shared.Response.LoginResult.Ok)
                {
                    await loginService.Login(loginResponse.Token!);
                    navigationManager.NavigateTo("");
                }
                else if (loginResponse.LoginResult == WebApi.Shared.Response.LoginResult.InvalidUsernamePassword)
                {
                    this.loginResultMessage = Resources.LoginResource.InvalidUsernamePassword;
                }
                else if (loginResponse.LoginResult == WebApi.Shared.Response.LoginResult.AccountExpired)
                {
                    this.loginResultMessage = Resources.LoginResource.AccountExpired;
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            finally
            {
                this.loginInProgress = false;
                this.StateHasChanged();
            }
        }
    }
}
