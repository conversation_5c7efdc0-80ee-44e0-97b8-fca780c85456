﻿using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace FluentBlue.Data.Model
{
    public partial class FluentBlueDbContext
    {
        public override int SaveChanges()
        {
            this.ApplyStateChanges();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            this.ApplyStateChanges();
            return SaveChangesAsync(acceptAllChangesOnSuccess: true, cancellationToken);
        }

        public void ApplyStateChanges()
        {
            foreach (var dbEntityEntry in this.ChangeTracker.Entries())
            {
                ////Ελέγχει αν το object έχει το ίδιο TenantId με τον τρέχον User.
                //PropertyInfo? tenantIdPropInfo = dbEntityEntry.Entity.GetType().GetProperty("TenantId");
                //if (tenantIdPropInfo != null)  //Αν το Entity έχει το property TenantId.
                //{
                //    if (tenantIdPropInfo.GetValue(dbEntityEntry.Entity)!.ToString() != currentTenantId.ToString())
                //    {
                //        throw new Exception("Missmatch of TenantId in Entity and current User.");
                //    }
                //}

                //Μεταφέρει το ObjectState στο EntryState
                var entityState = dbEntityEntry.Entity as IObjectState;
                if (entityState == null)
                    throw new InvalidCastException(
                        "All entites must implement " +
                        "the IObjectState interface, this interface " +
                        "must be implemented so each entites state" +
                        "can explicitely determined when updating graphs.");

                dbEntityEntry.State = ConvertState(entityState.ObjectState);

                //TODO: Να διαγραφεί το παρακάτω, οδηγεί σε λάθος συμπεριφορά.
                //// When an EventReminder is modified, prevent the PushSent property from being saved in database.
                //if (dbEntityEntry.Entity is EventReminder && dbEntityEntry.State == EntityState.Modified)
                //{
                //    // Prevent PushSent from being updated
                //    dbEntityEntry.Property(nameof(EventReminder.PushSent)).IsModified = false;
                //}
            }
        }

        private EntityState ConvertState(ObjectState state)
        {
            switch (state)
            {
                case ObjectState.Added:
                    return EntityState.Added;
                case ObjectState.Modified:
                    return EntityState.Modified;
                case ObjectState.Deleted:
                    return EntityState.Deleted;
                default:
                    return EntityState.Unchanged;
            }
        }
    }
}
